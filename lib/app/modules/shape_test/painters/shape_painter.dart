import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/shape_data.dart';
import '../utils/hit_test_utils.dart';
import '../utils/geometry_utils.dart';

class ShapePainter extends CustomPainter implements PathProvider {
  final ShapeData shapeData;
  final bool curveMode;
  final BoxConstraints constraints;
  final bool selected;

  ShapePainter({
    required this.shapeData,
    required this.curveMode,
    required this.constraints,
    this.selected = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Safety check for null or zero-sized canvas
    if (size.isEmpty) return;

    // Create fill paint
    final fillPaint = Paint()
      ..color = Get.theme.colorScheme.primary.withOpacity(selected ? 0.25 : 0.2)
      ..style = PaintingStyle.fill;

    // Create stroke paint
    final strokePaint = Paint()
      ..color = selected ? Colors.blue.shade700 : Get.theme.colorScheme.primary
      ..style = PaintingStyle.stroke
      ..strokeWidth = selected ? 3.0 : 2.0;

    // Use the centralized path building function for consistency
    final path = GeometryUtils.buildShapePath(shapeData);

    try {
      canvas.save();
      canvas.translate(shapeData.center.dx, shapeData.center.dy);
      canvas.rotate(shapeData.rotation);
      canvas.translate(-shapeData.center.dx, -shapeData.center.dy);

      // Draw fill first, then stroke
      canvas.drawPath(path, fillPaint);
      canvas.drawPath(path, strokePaint);

      // If in curve mode and selected, draw the curve control indicators
      if (curveMode && selected) {
        _drawCurveControls(canvas);
      }

      // Special visualization for custom shapes when selected
      if (selected && shapeData.type == ShapeType.custom) {
        _drawCustomShapeVertexIndicators(canvas);
      }
    } finally {
      // Ensure canvas.restore() is always called
      canvas.restore();
    }

    // Draw dotted bounding box for selected shapes outside the transformation
    // This ensures the bounding box is drawn in screen coordinates
    if (selected) {
      _drawDottedBoundingBox(canvas);
    }
  }

  // Get the shape path for hit testing - implement PathProvider interface
  @override
  Path getPath() {
    // Use the centralized path building function for consistency
    final path = GeometryUtils.buildShapePath(shapeData);
    final matrix = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);
    return path.transform(matrix.storage);
  }

  // Build a path with straight lines
  Path _buildStraightPath() {
    // For backwards compatibility, redirect to the centralized method if no curves
    return GeometryUtils.buildShapePath(shapeData);
  }

  // Build a path with curved lines where curve controls exist
  Path _buildCurvedPath() {
    // For backwards compatibility, redirect to the centralized method
    return GeometryUtils.buildShapePath(shapeData);
  }

  // Draw visual indicators for curve controls in curve mode
  void _drawCurveControls(Canvas canvas) {
    // Don't draw curves for group shapes
    if (shapeData.type == ShapeType.group) return;

    final controlPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.fill;

    final linePaint = Paint()
      ..color = Colors.red.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw legacy quadratic curve controls
    for (int i = 0; i < shapeData.vertices.length; i++) {
      if (!shapeData.curveControls.containsKey(i)) continue;

      final controlOffset = shapeData.curveControls[i]!;
      if (controlOffset == Offset.zero) continue;

      final startVertex = shapeData.vertices[i];
      final endVertex = shapeData.vertices[(i + 1) % shapeData.vertices.length];
      final midpoint = Offset(
        (startVertex.dx + endVertex.dx) / 2,
        (startVertex.dy + endVertex.dy) / 2,
      );

      final controlPoint = midpoint + controlOffset;

      // Draw line from midpoint to control point
      canvas.drawLine(midpoint, controlPoint, linePaint);

      // Draw control point
      canvas.drawCircle(controlPoint, 3, controlPaint);
    }

    // Draw cubic curve controls
    final cubicControlPaint1 = Paint()
      ..color = Colors.purple
      ..style = PaintingStyle.fill;

    final cubicControlPaint2 = Paint()
      ..color = Colors.deepPurple
      ..style = PaintingStyle.fill;

    final cubicLinePaint = Paint()
      ..color = Colors.purple.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    for (int i = 0; i < shapeData.vertices.length; i++) {
      if (!shapeData.hasEdgeCubicCurve(i)) continue;

      final controls = shapeData.getEdgeCubicControls(i);
      final startVertex = shapeData.vertices[i];
      final endVertex = shapeData.vertices[(i + 1) % shapeData.vertices.length];
      final midpoint = Offset(
        (startVertex.dx + endVertex.dx) / 2,
        (startVertex.dy + endVertex.dy) / 2,
      );

      // Calculate control point positions
      final controlPoint1 = midpoint + controls[0];
      final controlPoint2 = midpoint + controls[1];

      // Draw lines from midpoint to control points
      canvas.drawLine(midpoint, controlPoint1, cubicLinePaint);
      canvas.drawLine(midpoint, controlPoint2, cubicLinePaint);

      // Draw control points
      canvas.drawCircle(controlPoint1, 3, cubicControlPaint1);
      canvas.drawCircle(controlPoint2, 3, cubicControlPaint2);
    }
  }

  // Draw a dotted bounding box around the shape when selected
  void _drawDottedBoundingBox(Canvas canvas) {
    // Use the accurate bounding rectangle calculation
    final rect = GeometryUtils.calculateAccurateBoundingRect(shapeData);

    final dashPath = Path();
    const dashWidth = 6.0;
    const dashSpace = 4.0;

    // Configure stroke paint for the dotted line
    final strokePaint = Paint()
      ..color = Colors.blue.shade600
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // Define the corners of the bounding box
    final topLeft = Offset(rect.left, rect.top);
    final topRight = Offset(rect.right, rect.top);
    final bottomRight = Offset(rect.right, rect.bottom);
    final bottomLeft = Offset(rect.left, rect.bottom);

    // Top edge
    _addDashedLine(dashPath, topLeft, topRight, dashWidth, dashSpace);

    // Right edge
    _addDashedLine(dashPath, topRight, bottomRight, dashWidth, dashSpace);

    // Bottom edge
    _addDashedLine(dashPath, bottomRight, bottomLeft, dashWidth, dashSpace);

    // Left edge
    _addDashedLine(dashPath, bottomLeft, topLeft, dashWidth, dashSpace);

    // Draw the dashed path
    canvas.drawPath(dashPath, strokePaint);
  }

  // Helper method to add a dashed line to a path
  void _addDashedLine(
      Path path, Offset start, Offset end, double dashWidth, double dashSpace) {
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final edgeLength = math.sqrt(dx * dx + dy * dy);

    // Normalize direction vector
    final dirX = dx / edgeLength;
    final dirY = dy / edgeLength;

    // Draw dashed line
    double drawn = 0.0;
    while (drawn < edgeLength) {
      final dashLength = math.min(dashWidth, edgeLength - drawn);
      final dashEndX = start.dx + dirX * (drawn + dashLength);
      final dashEndY = start.dy + dirY * (drawn + dashLength);

      path.moveTo(start.dx + dirX * drawn, start.dy + dirY * drawn);
      path.lineTo(dashEndX, dashEndY);

      drawn += dashLength + dashSpace;
    }
  }

  // Helper method to draw vertex indicators for custom shapes
  void _drawCustomShapeVertexIndicators(Canvas canvas) {
    final vertexPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.fill;

    final vertexStrokePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw a small circle at each vertex
    for (final vertex in shapeData.vertices) {
      // No need to transform vertex since we're already in the transformed canvas context
      canvas.drawCircle(vertex, 4.0, vertexPaint);
      canvas.drawCircle(vertex, 4.0, vertexStrokePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
